import {DynamoDBClient} from "@aws-sdk/client-dynamodb";
import {DynamoDBDocumentClient, GetCommand, PutCommand, UpdateCommand} from "@aws-sdk/lib-dynamodb";
import {ListOfTickers} from "../entities/consolidated_data/ListOfTickers";
import {dynamoClient} from "../factory";
import {FundamentalDataType} from "../utils/types/EODHD/FundamentalDataType";
import {CashFlowController} from "./CashFlowController";
import {BalanceSheetController} from "./balanceSheetController";
import {IncomeStatementController} from "./incomeStatementController";
import {LogsController} from "./logsController";

export class DynamoController {
    client: DynamoDBClient;

    constructor() {
        this.client = dynamoClient;
    }

    async saveFundamentalData(data: FundamentalDataType) {
        try {
            const {ticker_internal_id, Financials} = data;

            const id = parseInt(ticker_internal_id || "0");

            const General = data?.General || {};

            const balance_sheet = Financials?.Balance_Sheet || {};
            const income_statement = Financials?.Income_Statement || {};
            const cash_flow = Financials?.Cash_Flow || {};

            const balanceSheet = new BalanceSheetController();
            const balanceSheetData = await balanceSheet.parseData(id, balance_sheet);
            console.log("balancesheet was QUEUED");

            const incomeStatement = new IncomeStatementController();
            const incomeStatementData = await incomeStatement.parseData(id, income_statement);
            console.log("income statement was QUEUED");

            const cashFlow = new CashFlowController();
            const cashFlowData = await cashFlow.parseData(id, cash_flow);
            console.log("cash flow was QUEUED");

            const logsController = new LogsController();

            if (!balanceSheetData || !incomeStatementData || !cashFlowData) {
                throw new Error("Missing data");
            }

            logsController.dynamoDBQueued(
                id,
                "Fundamental data, Balance Sheet, Income Statement and Cash Flow QUEUED successfully",
                balanceSheetData.quantity_of_balance_sheet_year,
                balanceSheetData.quantity_of_balance_sheet_quarter,
                cashFlowData.quantity_of_cash_flow_year,
                cashFlowData.quantity_of_cash_flow_quarter,
                incomeStatementData.quantity_of_income_statement_year,
                incomeStatementData.quantity_of_income_statement_quarter,
                balanceSheetData.start_of_balance_sheet_year,
                balanceSheetData.end_of_balance_sheet_year,
                cashFlowData.start_of_cash_flow_year,
                cashFlowData.end_of_cash_flow_year,
                incomeStatementData.start_of_income_statement_year,
                incomeStatementData.end_of_income_statement_year,
                balanceSheetData.start_of_balance_sheet_quarter,
                balanceSheetData.end_of_balance_sheet_quarter,
                cashFlowData.start_of_cash_flow_quarter,
                cashFlowData.end_of_cash_flow_quarter,
                incomeStatementData.start_of_income_statement_quarter,
                incomeStatementData.end_of_income_statement_quarter,
            );
        } catch (err: any) {
            console.log("error dynamo", err.message);
        }
    }

    async getDynamoData(key: string, table_name: string) {
        try {
            const docClient = DynamoDBDocumentClient.from(this.client);

            const command = new GetCommand({
                TableName: table_name,
                Key: {
                    ticker_internal_id: String(key),
                },
            });

            const response = await docClient.send(command);

            return response;
        } catch (error: any) {
            console.log("error when try to get dynamo", error.message);
        }
    }

    async saveDynamoData(key: string, table_name: string, body: any) {
        try {
            const docClient = DynamoDBDocumentClient.from(this.client);

            console.log(key, body);

            const command = new PutCommand({
                TableName: table_name,
                Item: {
                    ticker_internal_id: String(key),
                    body: JSON.stringify(body),
                },
            });

            const response = await docClient.send(command);

            return response;
        } catch (error: any) {
            console.log("error when try to save dynamo", error.message);
        }
    }
    async updateDynamoData(key: string, table_name: string, body: any) {
        try {
            const docClient = DynamoDBDocumentClient.from(this.client);

            const command = new UpdateCommand({
                TableName: table_name,
                Key: {
                    ticker_internal_id: String(key),
                },
                UpdateExpression: "set body = :body",
                ExpressionAttributeValues: {
                    ":body": JSON.stringify(body),
                },
                ReturnValues: "ALL_NEW",
            });

            const response = await docClient.send(command);

            return response;
        } catch (error: any) {
            console.log("error when try to update dynamo", error.message);
        }
    }
}
