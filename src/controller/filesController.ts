import {iFundamentalData} from "../entities/EOD/iFundamentalData";
import {ListOfTickers} from "../entities/consolidated_data/ListOfTickers";
import {StatisticsOfTicker} from "../entities/consolidated_data/StatisticsOfTickers";
import {LogsOfTickersExecute} from "../execute/logsOfTickersExecute";
import {FundamentalDataRepository} from "../repositories/implements/FundamentaDataRepository";
import {FundamentalDataType} from "../utils/types/EODHD/FundamentalDataType";
import {S3ObjectEvent, S3Record} from "../utils/types/s3/TriggerS3EventType";
import {DynamoController} from "./dynamoController";
import {Op} from "sequelize";

/**
 * Get tickers that had their fundamental_data_last_updated changed today
 * @returns Array of tickers updated today
 */
async function getTickersUpdatedToday(): Promise<ListOfTickers[]> {
    console.log("Retrieving tickers updated today");
    const startTime = Date.now();

    // Create start of today date
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    console.log(`Today's date (start): ${today.toISOString()}`);

    const params: any = {
        attributes: ["primary_ticker_eodhd", "id"],
        order: [["id", "ASC"]],
        where: {
            is_enable: 1,
            fundamental_data_last_updated: {
                [Op.gte]: today,
            },
        },
    };

    const updatedTickers = await ListOfTickers.findAll(params);
    const executionTime = Date.now() - startTime;

    console.log(`Found ${updatedTickers.length} tickers updated today in ${executionTime}ms`);
    return updatedTickers;
}

export async function processS3Files(): Promise<any> {
    const fundamentalData = new FundamentalDataRepository();
    const dynamoController = new DynamoController();

    // If no records provided, get tickers updated today and process their S3 files
    const updatedTickers = await getTickersUpdatedToday();
    console.log(`Processing ${updatedTickers.length} tickers updated today`);

    for (let i = 0; i < updatedTickers.length; i++) {
        const ticker = updatedTickers[i];
        const key = `fundamentals_${ticker.primary_ticker_eodhd}_eod.json`;

        try {
            console.log(`Processing ticker ${i + 1}/${updatedTickers.length}: ${ticker.primary_ticker_eodhd} (ID: ${ticker.id})`);
            const file: FundamentalDataType = await fundamentalData.get(key);
            await dynamoController.saveFundamentalData(file);
        } catch (error: any) {
            console.log(`Error processing ticker ${ticker.primary_ticker_eodhd}: ${error.message}`);
        }
    }
}

export async function moveFilesController(files: any, keys: any) {
    const fundamentalData = new FundamentalDataRepository();

    for (let i = 0; i < files.length; i++) {
        try {
            await fundamentalData.move(keys[i].Key, "parsed");
            await LogsOfTickersExecute.saveLogToMoveFile(files[i].ticker_internal_id, "File moved successfully");
        } catch (error: any) {
            console.log(error.message);
        }
    }
}

export async function searchFileTickerName(tickers: StatisticsOfTicker[]) {
    const fundamentalData = new FundamentalDataRepository();

    for (let i = 0; i < tickers.length; i++) {
        const ticker = tickers[i];

        const file: iFundamentalData = await fundamentalData.get(`fundamentals_${ticker.symbol_code}_eod.json`);

        const {Highlights: Highlights} = file;

        const dividend = Highlights?.DividendYield || 0;

        ticker.dividend_yield = dividend * 100;

        console.log("dividend ", ticker.dividend_yield, ticker.symbol_code, " saved");

        await ticker.save();
    }
}
