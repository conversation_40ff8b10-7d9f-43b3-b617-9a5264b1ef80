import axios from "axios";
import {ListOfTickers} from "../entities/consolidated_data/ListOfTickers";
import {FundamentalDataRepository} from "../repositories/implements/FundamentaDataRepository";
import {ListOfTickersRepository} from "../repositories/implements/ListOfTickersRepository";
import {apiEOD} from "../services/apiEOD";
import {ExchangeAPI, ResponseTicker, TickerType, TickerTypeDataBase} from "../utils/types/EODHD/exchange_countries";
import {LogsController} from "./logsController";

export async function populateTickersAPI_EOD() {
    try {
        console.log("populateTickersAPI_EOD ==> ", "Get all tickers from EOD API");
        let startTime = Date.now();

        // NYSE
        const {data: NYSE}: {data: ResponseTicker[]} = await apiEOD.populateTickers(ExchangeAPI.USA.NYSE.getExchangeCode());
        console.log(`NYSE API call completed in ${Date.now() - startTime}ms, retrieved ${NYSE.length} tickers`);

        // NASDAQ
        startTime = Date.now();
        const {data: NASDAQ}: {data: ResponseTicker[]} = await apiEOD.populateTickers(ExchangeAPI.USA.NASDAQ.getExchangeCode());
        console.log(`NASDAQ API call completed in ${Date.now() - startTime}ms, retrieved ${NASDAQ.length} tickers`);

        // BVMF (Brazil)
        startTime = Date.now();
        const {data: BVMF}: {data: ResponseTicker[]} = await apiEOD.populateTickers(ExchangeAPI.BRAZIL.BOVESPA.getExchangeCode());
        console.log(`BVMF API call completed in ${Date.now() - startTime}ms, retrieved ${BVMF.length} tickers`);

        // XOSL (Norway)
        startTime = Date.now();
        const {data: XOSL}: {data: ResponseTicker[]} = await apiEOD.populateTickers(ExchangeAPI.NORWAY.OSLO.getExchangeCode());
        console.log(`XOSL API call completed in ${Date.now() - startTime}ms, retrieved ${XOSL.length} tickers`);

        // XPAR (France)
        startTime = Date.now();
        const {data: XPAR}: {data: ResponseTicker[]} = await apiEOD.populateTickers(ExchangeAPI.FRANCE.EURONEXT.getExchangeCode());
        console.log(`XPAR API call completed in ${Date.now() - startTime}ms, retrieved ${XPAR.length} tickers`);

        // XETR (Germany)
        startTime = Date.now();
        const {data: XETR}: {data: ResponseTicker[]} = await apiEOD.populateTickers(ExchangeAPI.GERMANY.XETRA.getExchangeCode());
        console.log(`XETR API call completed in ${Date.now() - startTime}ms, retrieved ${XETR.length} tickers`);

        // XBRU (Belgium)
        startTime = Date.now();
        const {data: XBRU}: {data: ResponseTicker[]} = await apiEOD.populateTickers(ExchangeAPI.BELGIUM.EURONEXT.getExchangeCode());
        console.log(`XBRU API call completed in ${Date.now() - startTime}ms, retrieved ${XBRU.length} tickers`);

        // XAMS (Netherlands)
        startTime = Date.now();
        const {data: XAMS}: {data: ResponseTicker[]} = await apiEOD.populateTickers(ExchangeAPI.NETHERLANDS.EURONEXT.getExchangeCode());
        console.log(`XAMS API call completed in ${Date.now() - startTime}ms, retrieved ${XAMS.length} tickers`);

        // XLIS (Portugal)
        startTime = Date.now();
        const {data: XLIS}: {data: ResponseTicker[]} = await apiEOD.populateTickers(ExchangeAPI.PORTUGAL.EURONEXT.getExchangeCode());
        console.log(`XLIS API call completed in ${Date.now() - startTime}ms, retrieved ${XLIS.length} tickers`);

        // XDUB (Ireland)
        startTime = Date.now();
        const {data: XDUB}: {data: ResponseTicker[]} = await apiEOD.populateTickers(ExchangeAPI.IRELAND.EURONEXT.getExchangeCode());
        console.log(`XDUB API call completed in ${Date.now() - startTime}ms, retrieved ${XDUB.length} tickers`);

        // LSE (UK)
        startTime = Date.now();
        const {data: LSE}: {data: ResponseTicker[]} = await apiEOD.populateTickers(ExchangeAPI.UNITED_KINGDOM.LONDON.getExchangeCode());
        console.log(`LSE API call completed in ${Date.now() - startTime}ms, retrieved ${LSE.length} tickers`);

        const data = {
            NYSE,
            NASDAQ,
            BVMF,
            XOSL,
            XPAR,
            XETR,
            XBRU,
            XAMS,
            XLIS,
            XDUB,
            LSE,
        };

        return data;
    } catch (error: any) {
        let message;

        if (axios.isAxiosError(error)) {
            console.log(error.response?.data);

            message = error.response?.data || error.message;
            // Do something with this error...
        } else {
            message = error.message;
        }

        throw Error("Error when fecth " + message);
    }
}

/*
---------------------------------------- B3 (Brasil Bolsa Balcão) --------------------------------------------
1. Common Stock (Ações Ordinárias)
Ticker format: 4 letters + "3"

Example:

PETR3 = Petrobras (common stock)

VALE3 = Vale (common stock)

ITUB3 = Itaú Unibanco (common stock)

The "3" at the end always means common shares (ações ordinárias).

2. Preferred Stock (Ações Preferenciais)
Ticker format: 4 letters + "4"

Example:

PETR4 = Petrobras preferred stock

ITUB4 = Itaú preferred stock

BBAS4 = Banco do Brasil preferred

The "4" indicates preferred shares (ações preferenciais).

Less common endings exist too:

"5", "6", "7"...: Indicate different classes of preferred stock (e.g., PNA, PNB), though this is rare and mostly legacy.


---------------------------------------- XETRA (Germany) --------------------------------------------
1. Ticker Format on XETRA
Tickers (known as WKN or ISIN) are not standardized 3- or 4-letter codes like in NYSE/NASDAQ.

Instead, companies are generally identified by:

ISIN (International Securities Identification Number)

WKN (Wertpapierkennnummer – German security ID number)

Name (Company name)

There is no universal ticker symbol system like "AAPL" or "PETR4" on XETRA. Some brokers/platforms may assign short trading symbols, but they are not official exchange identifiers.

2. ISIN – Main Identifier
Germany-listed stocks always have an ISIN starting with DE.

Example:

Siemens AG → DE0007236101

BMW AG → DE0005190003

SAP SE → DE0007164600

3. WKN – Local German Code
A 6-character alphanumeric code (older than ISIN).

Examples:

Siemens → 723610

BMW → 519000

SAP → 716460

4. Share Classes
German companies often issue multiple share classes:

Stammaktien (common shares) and

Vorzugsaktien (preferred shares)

These are differentiated by ISIN/WKN and sometimes by an added identifier in broker systems.

Example – Volkswagen AG:
Type	ISIN	Description
Common (Stammaktien)	DE0007664005	Voting rights
Preferred (Vorzugsaktien)	DE0007664039	No voting rights, higher dividends

These don’t have suffixes like .A or -P; the difference is in the ISIN and security name.

5. Exchange Code (used on platforms)
On data services like Yahoo Finance or Bloomberg, you’ll often see:

BMW.DE, SAP.DE, DTE.DE (for Deutsche Telekom)

The .DE suffix simply indicates trading on German markets (like .PA for Paris or .SA for Brazil).

---------------------------------------- NYSE - New York Stock Exchange --------------------------------------------
1. Common Stock:
Ticker format: Usually 1 to 3 letters (sometimes 4)

Example:

F = Ford Motor Company

KO = Coca-Cola

GE = General Electric

IBM = International Business Machines

These tickers represent common shares, which are the standard equity ownership in a company.

2. Preferred Stock:
Ticker format: Common stock ticker plus a suffix, often after a dot or hyphen:

Format: TICKER.PRA, TICKER-PA, or TICKER+A

Example:

BRK.A = Berkshire Hathaway Class A Common Stock

BRK.B = Berkshire Hathaway Class B Common Stock

JPM.PRJ or JPM-J = JPMorgan Chase Series J Preferred Stock

NYSE may display preferreds as:

JPM-PRJ or JPM^J (system-dependent formatting)

The suffix usually indicates the series or class of preferred stock:

PR = preferred

A, B, C, etc. = series identifier

---------------------------------------- NASDAQ --------------------------------------------
1. Common Stock on NASDAQ
Usually 4 letters, sometimes 5.

Example:

AAPL = Apple Inc.

MSFT = Microsoft Corporation

TSLA = Tesla Inc.

These tickers without a suffix represent common stock.

2. Preferred Stock on NASDAQ
5-letter tickers, ending in a single letter suffix.

The suffix tells you the class or series of preferred stock.

Common suffixes:
Suffix	Meaning
A, B, C...	Preferred stock series (e.g., Series A, Series B)
W	Warrants
R	Rights
U	Units (esp. for SPACs)
Z	Miscellaneous security

Examples:
GOOGL = Alphabet Inc. Class A common stock

GOOG = Alphabet Inc. Class C common stock (no voting rights)

NVDAA = Hypothetical preferred Series A stock for NVIDIA (if it existed)

PLTRW = Palantir warrants

CCV.U = Churchill Capital SPAC unit


---------------------------------------- Euronext Paris, Amsterdam, Brussels and Lisbon --------------------------------------------
1. Common Stock Tickers on Euronext Paris
Usually 3 to 5 letters (often 4)

Generally simple and based on the company name.

Examples:
Ticker	Company
AIR	Airbus SE
ORA	Orange S.A.
BNP	BNP Paribas
MC	LVMH Moët Hennessy Louis Vuitton
SAN	Sanofi

These typically represent common equity shares.

2. Preferred Stock Tickers on Euronext Paris
Preferred or classed shares are rarely used in France.

When used, they typically have different ISINs rather than ticker suffixes.

If a company does issue multiple share classes, they’ll often be listed under a different mnemonic (ticker) and have a different ISIN, but this is not common practice like in the U.S.

3. ISIN-Based Identification
The ISIN (International Securities Identification Number) is the main unique identifier on Euronext.

Format: FRxxxxxxxxxx for French securities

Example:

LVMH → ISIN: FR0000121014

Sanofi → ISIN: FR0000120578

Unlike NYSE/NASDAQ where tickers carry more meaning (e.g., class or preferred type), Euronext relies more on ISINs for precise identification.


---------------------------------------- Oslo Stock Exchange --------------------------------------------
1. Ticker Symbols
Tickers on Oslo Børs are usually 1 to 4 uppercase letters, often reflecting the company name.

They do not include suffixes for share classes like .A, .P, -B, or numbers (3, 4, etc.).

If there are multiple share classes (rare), differentiation is typically not in the ticker itself, but via ISINs or a variant in the security name.

Examples:
Ticker	Company Name
EQNR	Equinor ASA (formerly Statoil)
DNB	DNB Bank ASA
YAR	Yara International ASA
TEL	Telenor ASA

2. ISIN – Main Identifier
ISINs for Norwegian stocks begin with NO.

Each security (even of the same company, if there are multiple classes) has its own ISIN.

Examples:
Company	Ticker	ISIN
Equinor ASA	EQNR	NO0010096985
DNB Bank ASA	DNB	NO0010031479
Yara International	YAR	NO0010208051

3. Preferred Shares or Multiple Classes
Very uncommon on Oslo Børs.

If issued, these are clearly described in the security name, not via ticker suffixes.

---------------------------------------- London Stock Exchange --------------------------------------------
1. Ticker Symbols
Tickers are typically 3 or 4 letters, but can range from 1 to 5.

Common shares usually have a straightforward ticker like HSBA for HSBC or VOD for Vodafone.

Preferred shares or different classes are often shown with suffixes or alternate tickers.

Examples:
Ticker	Company Name
HSBA	HSBC Holdings plc
VOD	Vodafone Group plc
RR.	Rolls-Royce Holdings plc
BP.	BP plc
GSK	GSK plc

Note: Some tickers include a dot (.) at the end (like RR. or BP.), which is a legacy from older LSE systems to distinguish securities or classes.

2. ISIN – Main Identifier
UK stocks have ISINs starting with GB.

Each class of share (e.g., ordinary vs. preferred) will have a unique ISIN.

Examples:
Company	Ticker	ISIN
HSBC Holdings	HSBA	GB0005405286
Vodafone Group	VOD	GB00BH4HKS39
BP	BP.	GB0007980591

3. Preferred Shares & Share Classes
Preferred shares, limited voting shares, and different classes (like Class A, Class B) are sometimes listed with separate tickers.

Example:

RDSA vs RDSB used to represent Royal Dutch Shell's A and B shares (now unified).

BRBY (Burberry Group) may have additional listings depending on share structure.

These classes are always differentiated by ticker and ISIN.

*/
export const populateListOfTickers = async () => {
    try {
        console.log("Populating list of tickers from EOD API");

        const exchangeData = await populateTickersAPI_EOD();
        const listOfTickersRepository = new ListOfTickersRepository();
        const tickersToCreateOrUpdate: Partial<ListOfTickers>[] = [];

        // Process BVMF (Brazilian) tickers
        tickersToCreateOrUpdate.push(...processExchangeTickers(exchangeData.BVMF, ExchangeAPI.BRAZIL.BOVESPA.getName(), ExchangeAPI.BRAZIL.BOVESPA.getExchangeCode()));

        // Process XETR (German) tickers
        tickersToCreateOrUpdate.push(...processExchangeTickers(exchangeData.XETR, ExchangeAPI.GERMANY.XETRA.getName(), ExchangeAPI.GERMANY.XETRA.getExchangeCode()));

        // Process NYSE (USA) tickers
        tickersToCreateOrUpdate.push(...processExchangeTickers(exchangeData.NYSE, ExchangeAPI.USA.NYSE.getName(), ExchangeAPI.USA.NYSE.getAlternativeExchangeCode()));

        // Process NASDAQ (USA) tickers
        tickersToCreateOrUpdate.push(...processExchangeTickers(exchangeData.NASDAQ, ExchangeAPI.USA.NASDAQ.getName(), ExchangeAPI.USA.NASDAQ.getAlternativeExchangeCode()));

        // Process XPAR (Euronext Paris) tickers
        tickersToCreateOrUpdate.push(...processExchangeTickers(exchangeData.XPAR, ExchangeAPI.FRANCE.EURONEXT.getName(), ExchangeAPI.FRANCE.EURONEXT.getExchangeCode()));

        // Process XAMS (Euronext Amsterdam) tickers
        tickersToCreateOrUpdate.push(...processExchangeTickers(exchangeData.XAMS, ExchangeAPI.NETHERLANDS.EURONEXT.getName(), ExchangeAPI.NETHERLANDS.EURONEXT.getExchangeCode()));

        // Process XBRU (Euronext Brussels) tickers
        tickersToCreateOrUpdate.push(...processExchangeTickers(exchangeData.XBRU, ExchangeAPI.BELGIUM.EURONEXT.getName(), ExchangeAPI.BELGIUM.EURONEXT.getExchangeCode()));

        // Process XLIS (Euronext Lisbon) tickers
        tickersToCreateOrUpdate.push(...processExchangeTickers(exchangeData.XLIS, ExchangeAPI.PORTUGAL.EURONEXT.getName(), ExchangeAPI.PORTUGAL.EURONEXT.getExchangeCode()));

        // Process XDUB (Euronext Dublin) tickers
        tickersToCreateOrUpdate.push(...processExchangeTickers(exchangeData.XDUB, ExchangeAPI.IRELAND.EURONEXT.getName(), ExchangeAPI.IRELAND.EURONEXT.getExchangeCode()));

        // Process XOSL (Norwegian) tickers
        tickersToCreateOrUpdate.push(...processExchangeTickers(exchangeData.XOSL, ExchangeAPI.NORWAY.OSLO.getName(), ExchangeAPI.NORWAY.OSLO.getExchangeCode()));

        // Process LSE (UK) tickers
        tickersToCreateOrUpdate.push(...processExchangeTickers(exchangeData.LSE, ExchangeAPI.UNITED_KINGDOM.LONDON.getName(), ExchangeAPI.UNITED_KINGDOM.LONDON.getExchangeCode()));

        // Bulk create the tickers
        const createdTickers = await listOfTickersRepository.bulkCreateOrUpdate(tickersToCreateOrUpdate);

        console.log(`END - Populated ${createdTickers.length} tickers`);
    } catch (error) {
        console.error("Error populating list of tickers:", error);
        throw error;
    }
};

// Function to process tickers from any exchange
export const processExchangeTickers = (tickers: ResponseTicker[], exchangeName: string, exchangeCode: string): Partial<ListOfTickers>[] => {
    const result: Partial<ListOfTickers>[] = [];
    const processedPreferredCodes = new Set<string>();
    const startTime = Date.now();

    console.log(`Processing ${tickers.length} tickers from ${exchangeName} with exchange code ${exchangeCode}`);

    // Special handling for Brazilian exchange (SA)
    if (exchangeCode === "SA") {
        // First pass: Process common stocks
        tickers.forEach((ticker) => {
            // For Brazilian tickers, get the first 4 letters
            const baseCode = ticker.Code.substring(0, 4);

            // Check if it's a common stock ending with exactly 3 or 11 and has ISIN
            if (ticker.Type === TickerType.COMMON_STOCK && (ticker.Code.endsWith("3") || ticker.Code.endsWith("11")) && ticker.Isin && ticker.Isin.trim() !== "") {
                const commonStock = result.find((t) => t.symbol_code && t.symbol_code.substring(0, 4) === baseCode && t.type === TickerTypeDataBase.COMMON_STOCK);
                if (commonStock) return;
                result.push({
                    symbol_code: ticker.Code,
                    country_code: ticker.Country.toUpperCase(),
                    exchange_code: exchangeName,
                    name: ticker.Name,
                    primary_ticker_eodhd: `${ticker.Code}.${exchangeCode}`,
                    currency_code: ticker.Currency,
                    isin: ticker.Isin,
                    type: TickerTypeDataBase.COMMON_STOCK,
                    url_endpoint: ticker.Code + "-" + ticker.Country.toUpperCase(),
                });
            }
        });

        // Second pass: Process preferred stocks
        tickers.forEach((ticker) => {
            // For Brazilian tickers, check if it's a preferred stock (ends with 3, 4, 5, 6, or 7)
            // or if it's labeled as common stock but has the preferred stock pattern
            const baseCode = ticker.Code.substring(0, 4);
            const isPreferredPattern =
                ticker.Code.endsWith("3") || ticker.Code.endsWith("4") || ticker.Code.endsWith("5") || ticker.Code.endsWith("6") || ticker.Code.endsWith("7") || ticker.Code.endsWith("11");

            if (isPreferredPattern) {
                // Find the common stock with the same base code
                const commonStock = result.find((t) => t.symbol_code && t.symbol_code.substring(0, 4) === baseCode);

                if (commonStock && commonStock.symbol_code !== ticker.Code) {
                    // Update the common stock's another_symbol_codes
                    if (commonStock.another_symbol_codes) {
                        commonStock.another_symbol_codes += `;${ticker.Code}`;
                    } else {
                        commonStock.another_symbol_codes = ticker.Code;
                    }
                }
            }
        });
    } else {
        // For other exchanges, use the original logic
        tickers.forEach((ticker, index) => {
            // Skip common stocks with empty ISIN
            if (ticker.Type === TickerType.COMMON_STOCK && (!ticker.Isin || ticker.Isin.trim() === "")) {
                return;
            }

            // Skip preferred stocks - they will be added only when processing their related common stock
            if (ticker.Type === TickerType.PREFERRED_STOCK) {
                return;
            }

            // Skip tickers with invalid ISIN by Country
            if (!isValidCountryIsin(ticker, exchangeCode)) {
                return;
            }

            // For common stocks, collect preferred stock codes
            let preferredCodes = [];
            let anotherSymbolCodes = "";

            if (ticker.Type === TickerType.COMMON_STOCK) {
                let nextIndex = index + 1;
                while (nextIndex < tickers.length && tickers[nextIndex].Type === TickerType.PREFERRED_STOCK && tickers[nextIndex].Code.startsWith(ticker.Code)) {
                    // Avoid adding the same preferred stock twice
                    const prefStock = tickers[nextIndex].Code;
                    if (!processedPreferredCodes.has(prefStock)) {
                        preferredCodes.push(prefStock);
                        processedPreferredCodes.add(prefStock);
                    }
                    nextIndex++;
                }

                if (preferredCodes.length > 0) {
                    anotherSymbolCodes = preferredCodes.join(";");
                }
            }

            result.push({
                symbol_code: ticker.Code,
                country_code: ticker.Country.toUpperCase(),
                exchange_code: exchangeName,
                name: ticker.Name,
                primary_ticker_eodhd: `${ticker.Code}.${exchangeCode}`,
                currency_code: ticker.Currency,
                isin: ticker.Isin,
                type:
                    ticker.Type === TickerType.COMMON_STOCK
                        ? TickerTypeDataBase.COMMON_STOCK
                        : ticker.Type === TickerType.PREFERRED_STOCK
                        ? TickerTypeDataBase.PREFERRED_STOCK
                        : ticker.Type === TickerType.FUND
                        ? TickerTypeDataBase.FUND
                        : ticker.Type === TickerType.MUTUAL_FUND
                        ? TickerTypeDataBase.MUTUAL_FUND
                        : ticker.Type === TickerType.ETF
                        ? TickerTypeDataBase.ETF
                        : "",
                another_symbol_codes: anotherSymbolCodes || undefined,
                url_endpoint: ticker.Code + "-" + ticker.Country.toUpperCase(),
            });
        });
    }
    console.log(`Processed ${tickers.length} tickers from ${exchangeName} in ${Date.now() - startTime}ms, resulting in ${result.length} tickers`);

    return result;
};

// Function to validate ticker ISIN by country code
const isValidCountryIsin = (ticker: ResponseTicker, exchangeCode: string): boolean => {
    // If ISIN is missing or empty, it's invalid
    if (!ticker.Isin || ticker.Isin.trim() === "") {
        return false;
    }

    // Map of exchange codes to their expected ISIN country prefixes
    const isinPrefixMap = {
        XETRA: "DE", // Germany
    };

    // Get the expected prefix for this exchange
    const expectedPrefix = isinPrefixMap[exchangeCode as keyof typeof isinPrefixMap];

    // If we don't have a mapping for this exchange, accept any ISIN
    if (!expectedPrefix) {
        return true;
    }

    // Check if the ISIN starts with the expected country prefix
    return ticker.Isin.startsWith(expectedPrefix);
};

async function getFundamentalDataAPI_EOD(stock_symbol: string) {
    try {
        console.log("Searching the API: ", stock_symbol);

        const {data} = await apiEOD.getFundamentaData(stock_symbol);

        return data;
    } catch (error: any) {
        let message;

        if (axios.isAxiosError(error)) {
            console.log(error.response?.data);

            message = error.response?.data || error.message;
            // Do something with this error...
        } else {
            message = error.message;
        }

        throw Error("Error when fecth " + message);
    }
}

export const getOutdatedListOfTickers = async () => {
    try {
        const listOfTickers = new ListOfTickersRepository();

        const tickers = await listOfTickers.getOutdatedTickers();

        return tickers;
    } catch (error) {
        throw error;
    }
};

export async function saveTickersIntoBucket(tickers: ListOfTickers[]) {
    console.log(`Starting to process ${tickers.length} tickers for fundamental data`);
    let successCount = 0;
    let errorCount = 0;

    for (let i = 0; i < tickers.length; i++) {
        const stock = tickers[i];

        // Skip if stock or id is undefined
        if (!stock || stock.id === undefined) {
            console.log(`Skipping ticker at index ${i}: Missing stock or undefined id`);
            errorCount++;
            continue;
        }

        const {primary_ticker_eodhd} = stock;
        console.log(`[${i + 1}/${tickers.length}] Processing ticker: ${primary_ticker_eodhd} (ID: ${stock.id})`);
        const logsController = new LogsController();
        try {
            console.log(`Fetching fundamental data for ${primary_ticker_eodhd}`);
            const data = await getFundamentalDataAPI_EOD(primary_ticker_eodhd);

            const fundamentalData = new FundamentalDataRepository();
            console.log(`Saving fundamental data for ${primary_ticker_eodhd}`);

            await fundamentalData.create(
                JSON.stringify({
                    ticker_internal_id: stock.id,
                    ...data,
                }),
                primary_ticker_eodhd,
            );
            stock.fundamental_data_last_updated = new Date();
            stock.updated_at = new Date();
            await stock.save();
            successCount++;
            console.log(`Successfully processed ${primary_ticker_eodhd}`);
            logsController.tickerUpdatedEODHD(stock.id, `Fundamental data updated with success and saved on S3for ${primary_ticker_eodhd}`);
        } catch (error: any) {
            errorCount++;
            console.log(`Error processing ${primary_ticker_eodhd}: ${error.message}`);
            if (error.message.includes("Symbol not found")) {
                stock.is_enable = 0;
                stock.log_eodhd = "Symbol not found";
                console.log(`Ticker ${primary_ticker_eodhd} disabled: Symbol not found`);
            } else if (axios.isAxiosError(error)) {
                // Handle network or API-related errors
                const statusCode = error.response?.status;
                const errorMessage = error.response?.data?.message || error.message;
                stock.log_eodhd = `API Error (${statusCode}): ${errorMessage}`;
                // Only disable ticker for certain error types (e.g., 404)
                if (statusCode === 404) {
                    stock.is_enable = 0;
                    console.log(`Ticker ${primary_ticker_eodhd} disabled: API returned 404`);
                } else {
                    console.log(`API error for ${primary_ticker_eodhd}: ${statusCode} - ${errorMessage}`);
                }
            } else {
                // Handle other types of errors
                stock.log_eodhd = `Error: ${error.message}`;
                console.log(`Unexpected error for ${primary_ticker_eodhd}: ${error.message}`);
            }
            stock.updated_at = new Date();
            await stock.save();
            logsController.tickerUpdatedEODHD(stock.id, stock.log_eodhd);
            continue;
        }
    }

    console.log(`Completed processing ${tickers.length} tickers. Success: ${successCount}, Errors: ${errorCount}`);
}
