"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { Calendar, Database, Home, List, Settings } from "lucide-react"
import { cn } from "@/lib/utils"
import styles from "./dashboard-sidebar.module.css"

export function DashboardSidebar() {
  const pathname = usePathname()

  const routes = [
    {
      name: "Dashboard",
      href: "/",
      icon: Home,
    },
    {
      name: "Jobs",
      href: "/jobs",
      icon: List,
    },
    {
      name: "Schedules",
      href: "/schedules",
      icon: Calendar,
    },
    {
      name: "Database",
      href: "/database",
      icon: Database,
    },
    {
      name: "Settings",
      href: "/settings",
      icon: Settings,
    },
  ]

  return (
    <div className={styles.sidebar}>
      <div className={styles.header}>
        <h1 className={styles.title}>Agenda Dashboard</h1>
      </div>
      <nav className={styles.nav}>
        {routes.map((route) => (
          <Link
            key={route.href}
            href={route.href}
            className={cn(styles.navLink, pathname === route.href && styles.activeNavLink)}
          >
            <route.icon className={styles.navIcon} />
            {route.name}
          </Link>
        ))}
      </nav>
      <div className={styles.footer}>
        <div className={styles.status}>
          <div className={styles.statusIndicator}></div>
          <span className={styles.statusText}>Connected</span>
        </div>
      </div>
    </div>
  )
}
