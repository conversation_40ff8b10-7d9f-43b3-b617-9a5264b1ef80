import {clsx, type ClassValue} from "clsx";
import {twMerge} from "tailwind-merge";
import {connectAgendaToMongoDB, isAgendaConnected} from "@/lib/agenda";

export function cn(...inputs: ClassValue[]) {
    return twMerge(clsx(inputs));
}

export const checkIfAgendaIsConnected = async () => {
    // Ensure MongoDB connection is established first
    if (!isAgendaConnected()) {
        await connectAgendaToMongoDB();

        // If still not connected, return appropriate error
        if (!isAgendaConnected()) {
            throw new Error("Database connection not available");
        }
    }
};
