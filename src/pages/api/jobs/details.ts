import type {NextApiRequest, NextApiResponse} from "next";
import {getJobsByName, getAgenda} from "../../../lib/agenda";
import {JobDetails} from "@/utils/types/agenda/job";
import {checkIfAgendaIsConnected} from "@/lib/utils";
import {ObjectId} from "mongodb";

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
    if (req.method !== "GET") {
        return res.status(405).json({error: "Method not allowed"});
    }

    await checkIfAgendaIsConnected();

    const {name, id} = req.query;

    if ((!name || typeof name !== "string") && (!id || typeof id !== "string")) {
        return res.status(400).json({error: "Job name or id is required"});
    }

    try {
        const agenda = getAgenda();
        let job;
        if (id && typeof id === "string") {
            // Get job by ID
            try {
                const objectId = new ObjectId(id);
                const jobs = await agenda.jobs({_id: objectId});
                console.log("Job details fetched:", jobs);
                job = jobs[0];
            } catch (error) {
                console.error("Invalid job ID:", error);
                return res.status(400).json({error: "Invalid job ID format"});
            }
        } else if (name && typeof name === "string") {
            // Get jobs by name
            const jobs = await getJobsByName(name);
            job = jobs[0]; // Get the first job with this name
        }

        if (!job) {
            return res.status(404).json({error: "Job not found"});
        }

        // Format job details
        const jobDetails: JobDetails = {
            _id: job.attrs._id.toString(),
            name: job.attrs.name,
            priority: job.attrs.priority,
            type: job.attrs.type,
            data: job.attrs.data || {},
            lastRunAt: job.attrs.lastRunAt,
            lastFinishedAt: job.attrs.lastFinishedAt,
            nextRunAt: job.attrs.nextRunAt,
            repeatInterval: job.attrs.repeatInterval || "manual",
            disabled: job.attrs.disabled,
        };
        console.log("Job details fetched:", jobDetails);
        return res.status(200).json({job: jobDetails});
    } catch (error) {
        console.error("Error fetching job details:", error);
        return res.status(500).json({error: "Failed to fetch job details"});
    }
}
